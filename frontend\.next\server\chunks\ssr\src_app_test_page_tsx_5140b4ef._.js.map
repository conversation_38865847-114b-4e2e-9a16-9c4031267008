{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/laragon/www/ecommerce/frontend/src/app/test/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { ProductService } from \"../../services\";\n\nexport default function TestPage() {\n  const [result, setResult] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const testDirectFetch = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      console.log(\"Making direct fetch request...\");\n      const response = await fetch(\"/api/products?featured=true\", {\n        method: \"GET\",\n        headers: {\n          Accept: \"application/json\",\n          \"Content-Type\": \"application/json\",\n        },\n      });\n\n      console.log(\"Response status:\", response.status);\n      console.log(\"Response headers:\", response.headers);\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n      console.log(\"Response data:\", data);\n      setResult(data);\n    } catch (err) {\n      console.error(\"Test error:\", err);\n      setError(err instanceof Error ? err.message : \"Unknown error\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"p-8\">\n      <h1 className=\"text-2xl font-bold mb-4\">API Test Page</h1>\n\n      <button\n        onClick={testDirectFetch}\n        disabled={loading}\n        className=\"bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50\"\n      >\n        {loading ? \"Testing...\" : \"Test Direct Fetch\"}\n      </button>\n\n      {error && (\n        <div className=\"mt-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded\">\n          Error: {error}\n        </div>\n      )}\n\n      {result && (\n        <div className=\"mt-4\">\n          <h2 className=\"text-lg font-semibold mb-2\">Result:</h2>\n          <pre className=\"bg-gray-100 p-4 rounded overflow-auto text-sm\">\n            {JSON.stringify(result, null, 2)}\n          </pre>\n\n          {result.data && Array.isArray(result.data) && (\n            <div className=\"mt-4\">\n              <h3 className=\"text-md font-semibold mb-2\">\n                Products ({result.data.length}):\n              </h3>\n              <ul className=\"list-disc list-inside\">\n                {result.data.map((product: any) => (\n                  <li key={product.id}>\n                    {product.name} - ${product.price}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,QAAQ;gBACR,SAAS;oBACP,QAAQ;oBACR,gBAAgB;gBAClB;YACF;YAEA,QAAQ,GAAG,CAAC,oBAAoB,SAAS,MAAM;YAC/C,QAAQ,GAAG,CAAC,qBAAqB,SAAS,OAAO;YAEjD,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,eAAe;YAC7B,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA0B;;;;;;0BAExC,8OAAC;gBACC,SAAS;gBACT,UAAU;gBACV,WAAU;0BAET,UAAU,eAAe;;;;;;YAG3B,uBACC,8OAAC;gBAAI,WAAU;;oBAAiE;oBACtE;;;;;;;YAIX,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,8OAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,CAAC,QAAQ,MAAM;;;;;;oBAG/B,OAAO,IAAI,IAAI,MAAM,OAAO,CAAC,OAAO,IAAI,mBACvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA6B;oCAC9B,OAAO,IAAI,CAAC,MAAM;oCAAC;;;;;;;0CAEhC,8OAAC;gCAAG,WAAU;0CACX,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,wBAChB,8OAAC;;4CACE,QAAQ,IAAI;4CAAC;4CAAK,QAAQ,KAAK;;uCADzB,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWrC", "debugId": null}}]}