import axios from 'axios';

// Create a simple Axios instance without CSRF token requirements
const simpleApi = axios.create({
    baseURL: '',
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    }
});

// Simple product fetching without authentication
export const fetchProductsSimple = async () => {
    try {
        console.log('Simple API: Fetching products...');
        const response = await simpleApi.get('/api/products');
        console.log('Simple API: Products response:', response.data);
        return response.data;
    } catch (error) {
        console.error('Simple API: Error fetching products:', error);
        throw error;
    }
};

// Simple category fetching without authentication
export const fetchCategoriesSimple = async () => {
    try {
        console.log('Simple API: Fetching categories...');
        const response = await simpleApi.get('/api/categories');
        console.log('Simple API: Categories response:', response.data);
        return response.data;
    } catch (error) {
        console.error('Simple API: Error fetching categories:', error);
        throw error;
    }
};

export default simpleApi;
